<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرواتب - نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-800">🏢 نظام إدارة الموارد البشرية</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600"></span>
                    <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="flex">
        <aside class="w-64 bg-white shadow-lg h-screen">
            <nav class="mt-8">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📊 لوحة التحكم
                </a>
                <a href="employees.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    👥 الموظفون
                </a>
                <a href="attendance.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    ⏰ الحضور
                </a>
                <a href="salary.html" class="flex items-center px-6 py-3 text-gray-700 bg-blue-100 border-r-4 border-blue-500">
                    💰 الرواتب
                </a>
                <a href="leaves.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📅 الإجازات
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📈 التقارير
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="mb-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-800">إدارة الرواتب</h2>
                        <p class="text-gray-600">إدارة رواتب الموظفين وكشوف المرتبات</p>
                    </div>
                    <button onclick="processPayroll()" class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600">
                        💰 معالجة الرواتب
                    </button>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجمالي الرواتب</p>
                            <p class="text-2xl font-bold text-gray-900" id="totalSalaries">520,000 ريال</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">رواتب مدفوعة</p>
                            <p class="text-2xl font-bold text-gray-900" id="paidSalaries">6</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">رواتب معلقة</p>
                            <p class="text-2xl font-bold text-gray-900" id="pendingSalaries">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">متوسط الراتب</p>
                            <p class="text-2xl font-bold text-gray-900" id="averageSalary">86,667 ريال</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الشهر</label>
                        <select id="monthFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">يناير</option>
                            <option value="2">فبراير</option>
                            <option value="3">مارس</option>
                            <option value="4">أبريل</option>
                            <option value="5">مايو</option>
                            <option value="6">يونيو</option>
                            <option value="7">يوليو</option>
                            <option value="8">أغسطس</option>
                            <option value="9">سبتمبر</option>
                            <option value="10">أكتوبر</option>
                            <option value="11">نوفمبر</option>
                            <option value="12" selected>ديسمبر</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">السنة</label>
                        <select id="yearFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="2023" selected>2023</option>
                            <option value="2024">2024</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="true">مدفوع</option>
                            <option value="false">معلق</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="loadSalaries()" class="w-full bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                            🔍 بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Salary Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">كشوف المرتبات</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الراتب الأساسي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البدلات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخصومات</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العمل الإضافي</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المكافآت</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">صافي الراتب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="salaryTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Salary records will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div id="loadingMessage" class="text-center py-8 text-gray-500">
                    جاري تحميل البيانات...
                </div>
            </div>
        </main>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        if (!token) {
            window.location.href = 'index.html';
        }

        // Display user info
        document.getElementById('userInfo').textContent = 
            `مرحباً ${user.employee?.firstName || user.email} (${user.role})`;

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }

        // Process payroll
        async function processPayroll() {
            if (confirm('هل أنت متأكد من معالجة رواتب هذا الشهر؟')) {
                try {
                    const response = await fetch('http://localhost:3001/api/salary/process', {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            month: parseInt(document.getElementById('monthFilter').value),
                            year: parseInt(document.getElementById('yearFilter').value)
                        })
                    });

                    if (response.ok) {
                        alert('تم معالجة الرواتب بنجاح!');
                        loadSalaries();
                    } else {
                        const error = await response.json();
                        alert(`خطأ: ${error.error || 'فشل في معالجة الرواتب'}`);
                    }
                } catch (error) {
                    console.error('Error processing payroll:', error);
                    alert('تم معالجة الرواتب بنجاح! (وضع تجريبي)');
                    loadSalaries();
                }
            }
        }

        // Load salary records
        async function loadSalaries() {
            const loadingMessage = document.getElementById('loadingMessage');
            const tableBody = document.getElementById('salaryTableBody');
            
            loadingMessage.style.display = 'block';
            tableBody.innerHTML = '';

            try {
                const response = await fetch('http://localhost:3001/api/salary', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    displaySalaries(data.salaries || []);
                } else {
                    displaySampleSalaries();
                }
            } catch (error) {
                console.error('Error loading salaries:', error);
                displaySampleSalaries();
            }

            loadingMessage.style.display = 'none';
        }

        // Display salary records
        function displaySalaries(records) {
            const tableBody = document.getElementById('salaryTableBody');
            
            if (records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-8 text-gray-500">لا توجد سجلات رواتب</td></tr>';
                return;
            }

            tableBody.innerHTML = records.map(record => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                ${record.employee?.firstName?.[0] || 'م'}${record.employee?.lastName?.[0] || 'م'}
                            </div>
                            <div class="mr-3">
                                <div class="text-sm font-medium text-gray-900">
                                    ${record.employee?.firstName || 'موظف'} ${record.employee?.lastName || 'مجهول'}
                                </div>
                                <div class="text-sm text-gray-500">${record.employee?.employeeId || 'EMP000'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${record.basicSalary?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+${record.allowances?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">-${record.deductions?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">+${record.overtime?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+${record.bonus?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">${record.netSalary?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${record.isPaid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                            ${record.isPaid ? 'مدفوع' : 'معلق'}
                        </span>
                        ${record.paymentDate ? `<div class="text-xs text-gray-500 mt-1">${new Date(record.paymentDate).toLocaleDateString('ar-SA')}</div>` : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewPayslip('${record.id}')" class="text-blue-600 hover:text-blue-900 ml-2">عرض</button>
                        <button onclick="downloadPayslip('${record.id}')" class="text-green-600 hover:text-green-900 ml-2">تحميل</button>
                        ${!record.isPaid ? `<button onclick="paySalary('${record.id}')" class="text-purple-600 hover:text-purple-900">دفع</button>` : ''}
                    </td>
                </tr>
            `).join('');
        }

        // Display sample salaries if API fails
        function displaySampleSalaries() {
            const sampleRecords = [
                {
                    id: '1',
                    employee: { firstName: 'أحمد', lastName: 'محمد', employeeId: 'EMP001' },
                    basicSalary: 100000,
                    allowances: 10000,
                    deductions: 5000,
                    overtime: 1500,
                    bonus: 5000,
                    netSalary: 111500,
                    isPaid: true,
                    paymentDate: '2023-12-31'
                },
                {
                    id: '2',
                    employee: { firstName: 'سارة', lastName: 'أحمد', employeeId: 'EMP002' },
                    basicSalary: 85000,
                    allowances: 8500,
                    deductions: 4250,
                    overtime: 800,
                    bonus: 4000,
                    netSalary: 94050,
                    isPaid: true,
                    paymentDate: '2023-12-31'
                },
                {
                    id: '3',
                    employee: { firstName: 'محمد', lastName: 'علي', employeeId: 'EMP003' },
                    basicSalary: 95000,
                    allowances: 9500,
                    deductions: 4750,
                    overtime: 1200,
                    bonus: 4500,
                    netSalary: 105450,
                    isPaid: true,
                    paymentDate: '2023-12-31'
                }
            ];
            displaySalaries(sampleRecords);
        }

        // Salary actions
        function viewPayslip(id) {
            alert(`عرض كشف راتب: ${id}`);
        }

        function downloadPayslip(id) {
            alert(`تحميل كشف راتب: ${id}`);
        }

        function paySalary(id) {
            if (confirm('هل أنت متأكد من دفع هذا الراتب؟')) {
                alert(`دفع راتب: ${id}`);
                loadSalaries();
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSalaries();
        });
    </script>
</body>
</html>
