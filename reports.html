<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-800">🏢 نظام إدارة الموارد البشرية</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600"></span>
                    <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="flex">
        <aside class="w-64 bg-white shadow-lg h-screen">
            <nav class="mt-8">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📊 لوحة التحكم
                </a>
                <a href="employees.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    👥 الموظفون
                </a>
                <a href="attendance.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    ⏰ الحضور
                </a>
                <a href="salary.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    💰 الرواتب
                </a>
                <a href="leaves.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📅 الإجازات
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 text-gray-700 bg-blue-100 border-r-4 border-blue-500">
                    📈 التقارير
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800">التقارير والإحصائيات</h2>
                <p class="text-gray-600">تقارير شاملة عن أداء الموارد البشرية</p>
            </div>

            <!-- Report Types -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="generateReport('attendance')">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">تقرير الحضور</h3>
                            <p class="text-sm text-gray-600">إحصائيات الحضور والغياب</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="generateReport('salary')">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">تقرير الرواتب</h3>
                            <p class="text-sm text-gray-600">تحليل الرواتب والمكافآت</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="generateReport('leaves')">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">تقرير الإجازات</h3>
                            <p class="text-sm text-gray-600">إحصائيات الإجازات والطلبات</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="generateReport('employees')">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">تقرير الموظفين</h3>
                            <p class="text-sm text-gray-600">إحصائيات الموظفين والأقسام</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="generateReport('performance')">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">تقرير الأداء</h3>
                            <p class="text-sm text-gray-600">تحليل أداء الموظفين</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="generateReport('custom')">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-indigo-100">
                            <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-semibold text-gray-800">تقرير مخصص</h3>
                            <p class="text-sm text-gray-600">إنشاء تقرير حسب الطلب</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Attendance Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">إحصائيات الحضور الشهرية</h3>
                        <button onclick="exportChart('attendanceChart')" class="text-blue-600 hover:text-blue-800 text-sm">
                            📥 تصدير
                        </button>
                    </div>
                    <canvas id="attendanceChart" width="400" height="200"></canvas>
                </div>

                <!-- Department Distribution -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">توزيع الموظفين حسب القسم</h3>
                        <button onclick="exportChart('departmentChart')" class="text-blue-600 hover:text-blue-800 text-sm">
                            📥 تصدير
                        </button>
                    </div>
                    <canvas id="departmentChart" width="400" height="200"></canvas>
                </div>

                <!-- Salary Trends -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">اتجاهات الرواتب</h3>
                        <button onclick="exportChart('salaryChart')" class="text-blue-600 hover:text-blue-800 text-sm">
                            📥 تصدير
                        </button>
                    </div>
                    <canvas id="salaryChart" width="400" height="200"></canvas>
                </div>

                <!-- Leave Types -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">أنواع الإجازات</h3>
                        <button onclick="exportChart('leaveChart')" class="text-blue-600 hover:text-blue-800 text-sm">
                            📥 تصدير
                        </button>
                    </div>
                    <canvas id="leaveChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">إحصائيات سريعة</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600" id="totalEmployeesCount">6</div>
                        <div class="text-sm text-gray-600">إجمالي الموظفين</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600" id="avgAttendanceRate">92%</div>
                        <div class="text-sm text-gray-600">معدل الحضور</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-yellow-600" id="pendingLeavesCount">2</div>
                        <div class="text-sm text-gray-600">طلبات إجازات معلقة</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-purple-600" id="avgSalary">86,667</div>
                        <div class="text-sm text-gray-600">متوسط الراتب (ريال)</div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        if (!token) {
            window.location.href = 'index.html';
        }

        // Display user info
        document.getElementById('userInfo').textContent = 
            `مرحباً ${user.employee?.firstName || user.email} (${user.role})`;

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }

        // Generate report
        function generateReport(type) {
            const reportTypes = {
                'attendance': 'تقرير الحضور',
                'salary': 'تقرير الرواتب',
                'leaves': 'تقرير الإجازات',
                'employees': 'تقرير الموظفين',
                'performance': 'تقرير الأداء',
                'custom': 'تقرير مخصص'
            };

            alert(`جاري إنشاء ${reportTypes[type]}...\nسيتم تحميل التقرير قريباً.`);
        }

        // Export chart
        function exportChart(chartId) {
            alert(`جاري تصدير الرسم البياني: ${chartId}`);
        }

        // Initialize charts
        function initCharts() {
            // Attendance Chart
            const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
            new Chart(attendanceCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'معدل الحضور (%)',
                        data: [95, 92, 88, 94, 91, 96],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { 
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // Department Chart
            const departmentCtx = document.getElementById('departmentChart').getContext('2d');
            new Chart(departmentCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الهندسة', 'الموارد البشرية', 'التسويق', 'المبيعات'],
                    datasets: [{
                        data: [3, 1, 1, 1],
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(245, 158, 11)',
                            'rgb(239, 68, 68)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });

            // Salary Chart
            const salaryCtx = document.getElementById('salaryChart').getContext('2d');
            new Chart(salaryCtx, {
                type: 'bar',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'إجمالي الرواتب (ألف ريال)',
                        data: [520, 525, 518, 530, 522, 535],
                        backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        borderColor: 'rgb(16, 185, 129)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // Leave Chart
            const leaveCtx = document.getElementById('leaveChart').getContext('2d');
            new Chart(leaveCtx, {
                type: 'pie',
                data: {
                    labels: ['سنوية', 'مرضية', 'طارئة', 'أمومة/أبوة'],
                    datasets: [{
                        data: [12, 5, 3, 2],
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(239, 68, 68)',
                            'rgb(245, 158, 11)',
                            'rgb(16, 185, 129)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // Load report data
        async function loadReportData() {
            try {
                // This would normally fetch data from the API
                // For now, we'll use static data
                console.log('Loading report data...');
            } catch (error) {
                console.error('Error loading report data:', error);
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            loadReportData();
        });
    </script>
</body>
</html>
