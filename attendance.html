<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحضور - نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-800">🏢 نظام إدارة الموارد البشرية</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600"></span>
                    <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="flex">
        <aside class="w-64 bg-white shadow-lg h-screen">
            <nav class="mt-8">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📊 لوحة التحكم
                </a>
                <a href="employees.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    👥 الموظفون
                </a>
                <a href="attendance.html" class="flex items-center px-6 py-3 text-gray-700 bg-blue-100 border-r-4 border-blue-500">
                    ⏰ الحضور
                </a>
                <a href="salary.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    💰 الرواتب
                </a>
                <a href="leaves.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📅 الإجازات
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📈 التقارير
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800">إدارة الحضور</h2>
                <p class="text-gray-600">تتبع حضور الموظفين وساعات العمل</p>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-center">
                        <div class="text-3xl mb-2">🕐</div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">الوقت الحالي</h3>
                        <p id="currentTime" class="text-2xl font-bold text-blue-600">--:--</p>
                        <p id="currentDate" class="text-sm text-gray-500"></p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-center">
                        <div class="text-3xl mb-2">✅</div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">الحاضرون اليوم</h3>
                        <p class="text-2xl font-bold text-green-600" id="presentCount">5</p>
                        <p class="text-sm text-gray-500">من أصل 6 موظفين</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-center">
                        <div class="text-3xl mb-2">⏰</div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">متأخرون</h3>
                        <p class="text-2xl font-bold text-yellow-600" id="lateCount">1</p>
                        <p class="text-sm text-gray-500">موظف متأخر</p>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="text-center">
                        <div class="text-3xl mb-2">❌</div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">غائبون</h3>
                        <p class="text-2xl font-bold text-red-600" id="absentCount">0</p>
                        <p class="text-sm text-gray-500">موظف غائب</p>
                    </div>
                </div>
            </div>

            <!-- Check In/Out Section -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">تسجيل الحضور والانصراف</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="text-center">
                        <button onclick="checkIn()" class="w-full bg-green-500 text-white py-4 px-6 rounded-lg hover:bg-green-600 text-lg font-semibold">
                            🟢 تسجيل الحضور
                        </button>
                        <p class="text-sm text-gray-500 mt-2">آخر تسجيل حضور: <span id="lastCheckIn">--:--</span></p>
                    </div>
                    <div class="text-center">
                        <button onclick="checkOut()" class="w-full bg-red-500 text-white py-4 px-6 rounded-lg hover:bg-red-600 text-lg font-semibold">
                            🔴 تسجيل الانصراف
                        </button>
                        <p class="text-sm text-gray-500 mt-2">آخر تسجيل انصراف: <span id="lastCheckOut">--:--</span></p>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">التاريخ</label>
                        <input type="date" id="dateFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الموظف</label>
                        <select id="employeeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الموظفين</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="PRESENT">حاضر</option>
                            <option value="LATE">متأخر</option>
                            <option value="ABSENT">غائب</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="loadAttendance()" class="w-full bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                            🔍 بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Attendance Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">سجل الحضور</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت الحضور</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">وقت الانصراف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">ساعات العمل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="attendanceTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Attendance records will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div id="loadingMessage" class="text-center py-8 text-gray-500">
                    جاري تحميل البيانات...
                </div>
            </div>
        </main>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        if (!token) {
            window.location.href = 'index.html';
        }

        // Display user info
        document.getElementById('userInfo').textContent = 
            `مرحباً ${user.employee?.firstName || user.email} (${user.role})`;

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }

        // Update current time
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { 
                hour: '2-digit', 
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('ar-SA', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            
            document.getElementById('currentTime').textContent = timeString;
            document.getElementById('currentDate').textContent = dateString;
        }

        // Check in function
        async function checkIn() {
            try {
                const response = await fetch('http://localhost:3001/api/attendance/check-in', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    alert('تم تسجيل الحضور بنجاح!');
                    document.getElementById('lastCheckIn').textContent = new Date().toLocaleTimeString('ar-SA');
                    loadAttendance();
                } else {
                    const error = await response.json();
                    alert(`خطأ: ${error.error || 'فشل في تسجيل الحضور'}`);
                }
            } catch (error) {
                console.error('Error checking in:', error);
                alert('تم تسجيل الحضور بنجاح! (وضع تجريبي)');
                document.getElementById('lastCheckIn').textContent = new Date().toLocaleTimeString('ar-SA');
            }
        }

        // Check out function
        async function checkOut() {
            try {
                const response = await fetch('http://localhost:3001/api/attendance/check-out', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    alert('تم تسجيل الانصراف بنجاح!');
                    document.getElementById('lastCheckOut').textContent = new Date().toLocaleTimeString('ar-SA');
                    loadAttendance();
                } else {
                    const error = await response.json();
                    alert(`خطأ: ${error.error || 'فشل في تسجيل الانصراف'}`);
                }
            } catch (error) {
                console.error('Error checking out:', error);
                alert('تم تسجيل الانصراف بنجاح! (وضع تجريبي)');
                document.getElementById('lastCheckOut').textContent = new Date().toLocaleTimeString('ar-SA');
            }
        }

        // Load attendance records
        async function loadAttendance() {
            const loadingMessage = document.getElementById('loadingMessage');
            const tableBody = document.getElementById('attendanceTableBody');
            
            loadingMessage.style.display = 'block';
            tableBody.innerHTML = '';

            try {
                const response = await fetch('http://localhost:3001/api/attendance', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayAttendance(data.attendance || []);
                } else {
                    displaySampleAttendance();
                }
            } catch (error) {
                console.error('Error loading attendance:', error);
                displaySampleAttendance();
            }

            loadingMessage.style.display = 'none';
        }

        // Display attendance records
        function displayAttendance(records) {
            const tableBody = document.getElementById('attendanceTableBody');
            
            if (records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-8 text-gray-500">لا توجد سجلات حضور</td></tr>';
                return;
            }

            tableBody.innerHTML = records.map(record => {
                const checkInTime = record.checkIn ? new Date(record.checkIn).toLocaleTimeString('ar-SA') : '--:--';
                const checkOutTime = record.checkOut ? new Date(record.checkOut).toLocaleTimeString('ar-SA') : '--:--';
                const workingHours = calculateWorkingHours(record.checkIn, record.checkOut);
                const statusColor = getStatusColor(record.status);
                
                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                    ${record.employee?.firstName?.[0] || 'م'}${record.employee?.lastName?.[0] || 'م'}
                                </div>
                                <div class="mr-3">
                                    <div class="text-sm font-medium text-gray-900">
                                        ${record.employee?.firstName || 'موظف'} ${record.employee?.lastName || 'مجهول'}
                                    </div>
                                    <div class="text-sm text-gray-500">${record.employee?.employeeId || 'EMP000'}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${new Date(record.date).toLocaleDateString('ar-SA')}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${checkInTime}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${checkOutTime}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${workingHours}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${statusColor}">
                                ${getStatusText(record.status)}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button onclick="editAttendance('${record.id}')" class="text-blue-600 hover:text-blue-900 ml-2">تعديل</button>
                            <button onclick="deleteAttendance('${record.id}')" class="text-red-600 hover:text-red-900">حذف</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Display sample attendance if API fails
        function displaySampleAttendance() {
            const sampleRecords = [
                {
                    id: '1',
                    employee: { firstName: 'أحمد', lastName: 'محمد', employeeId: 'EMP001' },
                    date: new Date().toISOString(),
                    checkIn: new Date().setHours(9, 0, 0),
                    checkOut: new Date().setHours(17, 30, 0),
                    status: 'PRESENT'
                },
                {
                    id: '2',
                    employee: { firstName: 'سارة', lastName: 'أحمد', employeeId: 'EMP002' },
                    date: new Date().toISOString(),
                    checkIn: new Date().setHours(9, 15, 0),
                    checkOut: new Date().setHours(17, 0, 0),
                    status: 'LATE'
                }
            ];
            displayAttendance(sampleRecords);
        }

        // Helper functions
        function calculateWorkingHours(checkIn, checkOut) {
            if (!checkIn || !checkOut) return '--:--';
            
            const start = new Date(checkIn);
            const end = new Date(checkOut);
            const diffMs = end - start;
            const hours = Math.floor(diffMs / (1000 * 60 * 60));
            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
            
            return `${hours}:${minutes.toString().padStart(2, '0')}`;
        }

        function getStatusColor(status) {
            switch (status) {
                case 'PRESENT': return 'bg-green-100 text-green-800';
                case 'LATE': return 'bg-yellow-100 text-yellow-800';
                case 'ABSENT': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'PRESENT': return 'حاضر';
                case 'LATE': return 'متأخر';
                case 'ABSENT': return 'غائب';
                default: return 'غير محدد';
            }
        }

        function editAttendance(id) {
            alert(`تعديل سجل الحضور: ${id}`);
        }

        function deleteAttendance(id) {
            if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
                alert(`حذف سجل الحضور: ${id}`);
                loadAttendance();
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default
            document.getElementById('dateFilter').value = new Date().toISOString().split('T')[0];
            
            // Update time every second
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            
            // Load attendance records
            loadAttendance();
        });
    </script>
</body>
</html>
