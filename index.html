<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">🏢 نظام إدارة الموارد البشرية</h1>
            <p class="text-lg text-gray-600">HR Management System</p>
        </div>

        <!-- Status Cards -->
        <div class="grid md:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center">
                    <div class="bg-green-100 p-3 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-semibold text-gray-800">الخادم الخلفي</h3>
                        <p class="text-green-600">يعمل على المنفذ 3001 ✅</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-3 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-semibold text-gray-800">الواجهة الأمامية</h3>
                        <p class="text-blue-600">جاهزة للاستخدام ✅</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Login Form -->
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-2xl font-bold text-center text-gray-800 mb-6">تسجيل الدخول</h2>

            <form id="loginForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input type="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                    <input type="password" id="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="password123">
                </div>

                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition duration-200">
                    تسجيل الدخول
                </button>
            </form>

            <div id="result" class="mt-4 p-3 rounded-md hidden"></div>
        </div>

        <!-- Demo Accounts -->
        <div class="max-w-2xl mx-auto mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">حسابات تجريبية</h3>
            <div class="grid md:grid-cols-2 gap-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800">مدير النظام</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">password123</p>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800">موارد بشرية</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">password123</p>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800">مدير</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">password123</p>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-gray-800">موظف</h4>
                    <p class="text-sm text-gray-600"><EMAIL></p>
                    <p class="text-sm text-gray-600">password123</p>
                </div>
            </div>
        </div>

        <!-- API Test -->
        <div class="max-w-2xl mx-auto mt-8 bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">اختبار API</h3>
            <button id="testApi" class="bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition duration-200">
                اختبار اتصال API
            </button>
            <div id="apiResult" class="mt-4 p-3 rounded-md hidden"></div>
        </div>
    </div>

    <script>
        // Test API Connection
        document.getElementById('testApi').addEventListener('click', async () => {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.className = 'mt-4 p-3 rounded-md';
            resultDiv.style.display = 'block';

            try {
                const response = await fetch('http://localhost:3001/api/health');
                const data = await response.json();

                resultDiv.className += ' bg-green-100 border border-green-400 text-green-700';
                resultDiv.innerHTML = `
                    <h4 class="font-bold">✅ API يعمل بنجاح!</h4>
                    <pre class="text-sm mt-2">${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.className += ' bg-red-100 border border-red-400 text-red-700';
                resultDiv.innerHTML = `
                    <h4 class="font-bold">❌ خطأ في الاتصال</h4>
                    <p class="text-sm mt-2">${error.message}</p>
                `;
            }
        });

        // Login Form
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');

            resultDiv.className = 'mt-4 p-3 rounded-md';
            resultDiv.style.display = 'block';

            try {
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    // Store authentication data
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('user', JSON.stringify(data.user));

                    resultDiv.className += ' bg-green-100 border border-green-400 text-green-700';
                    resultDiv.innerHTML = `
                        <h4 class="font-bold">✅ تم تسجيل الدخول بنجاح!</h4>
                        <p class="text-sm mt-2">مرحباً ${data.user.employee?.firstName || data.user.email}</p>
                        <p class="text-sm">الدور: ${data.user.role}</p>
                        <p class="text-sm mt-2">جاري التوجيه إلى لوحة التحكم...</p>
                    `;

                    // Redirect to dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 2000);
                } else {
                    resultDiv.className += ' bg-red-100 border border-red-400 text-red-700';
                    resultDiv.innerHTML = `
                        <h4 class="font-bold">❌ فشل تسجيل الدخول</h4>
                        <p class="text-sm mt-2">${data.error || 'خطأ غير معروف'}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className += ' bg-red-100 border border-red-400 text-red-700';
                resultDiv.innerHTML = `
                    <h4 class="font-bold">❌ خطأ في الاتصال</h4>
                    <p class="text-sm mt-2">${error.message}</p>
                `;
            }
        });
    </script>
</body>
</html>
