<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإجازات - نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-800">🏢 نظام إدارة الموارد البشرية</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600"></span>
                    <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="flex">
        <aside class="w-64 bg-white shadow-lg h-screen">
            <nav class="mt-8">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📊 لوحة التحكم
                </a>
                <a href="employees.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    👥 الموظفون
                </a>
                <a href="attendance.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    ⏰ الحضور
                </a>
                <a href="salary.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    💰 الرواتب
                </a>
                <a href="leaves.html" class="flex items-center px-6 py-3 text-gray-700 bg-blue-100 border-r-4 border-blue-500">
                    📅 الإجازات
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📈 التقارير
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="mb-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-800">إدارة الإجازات</h2>
                        <p class="text-gray-600">إدارة طلبات الإجازات والموافقات</p>
                    </div>
                    <button onclick="showRequestLeaveModal()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                        ➕ طلب إجازة جديدة
                    </button>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                            <p class="text-2xl font-bold text-gray-900" id="pendingLeaves">2</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجازات موافق عليها</p>
                            <p class="text-2xl font-bold text-gray-900" id="approvedLeaves">5</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجازات مرفوضة</p>
                            <p class="text-2xl font-bold text-gray-900" id="rejectedLeaves">1</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجمالي الأيام</p>
                            <p class="text-2xl font-bold text-gray-900" id="totalDays">25</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="PENDING">معلقة</option>
                            <option value="APPROVED">موافق عليها</option>
                            <option value="REJECTED">مرفوضة</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الإجازة</label>
                        <select id="typeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="ANNUAL">سنوية</option>
                            <option value="SICK">مرضية</option>
                            <option value="MATERNITY">أمومة</option>
                            <option value="PATERNITY">أبوة</option>
                            <option value="EMERGENCY">طارئة</option>
                            <option value="UNPAID">بدون راتب</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الموظف</label>
                        <select id="employeeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الموظفين</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="loadLeaves()" class="w-full bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                            🔍 بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Leaves Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">طلبات الإجازات</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">نوع الإجازة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المدة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الأيام</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السبب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="leavesTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Leave records will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div id="loadingMessage" class="text-center py-8 text-gray-500">
                    جاري تحميل البيانات...
                </div>
            </div>
        </main>
    </div>

    <!-- Request Leave Modal -->
    <div id="requestLeaveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">طلب إجازة جديدة</h3>
                </div>
                <form id="requestLeaveForm" class="p-6 space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">نوع الإجازة</label>
                        <select name="type" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر نوع الإجازة</option>
                            <option value="ANNUAL">سنوية</option>
                            <option value="SICK">مرضية</option>
                            <option value="MATERNITY">أمومة</option>
                            <option value="PATERNITY">أبوة</option>
                            <option value="EMERGENCY">طارئة</option>
                            <option value="UNPAID">بدون راتب</option>
                        </select>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية</label>
                            <input type="date" name="startDate" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ النهاية</label>
                            <input type="date" name="endDate" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">السبب</label>
                        <textarea name="reason" required rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="اكتب سبب الإجازة..."></textarea>
                    </div>
                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" onclick="hideRequestLeaveModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                            إلغاء
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                            إرسال الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        if (!token) {
            window.location.href = 'index.html';
        }

        // Display user info
        document.getElementById('userInfo').textContent = 
            `مرحباً ${user.employee?.firstName || user.email} (${user.role})`;

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }

        // Load leave records
        async function loadLeaves() {
            const loadingMessage = document.getElementById('loadingMessage');
            const tableBody = document.getElementById('leavesTableBody');
            
            loadingMessage.style.display = 'block';
            tableBody.innerHTML = '';

            try {
                const response = await fetch('http://localhost:3001/api/leaves', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayLeaves(data.leaves || []);
                } else {
                    displaySampleLeaves();
                }
            } catch (error) {
                console.error('Error loading leaves:', error);
                displaySampleLeaves();
            }

            loadingMessage.style.display = 'none';
        }

        // Display leave records
        function displayLeaves(records) {
            const tableBody = document.getElementById('leavesTableBody');
            
            if (records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-8 text-gray-500">لا توجد طلبات إجازات</td></tr>';
                return;
            }

            tableBody.innerHTML = records.map(record => {
                const statusColor = getLeaveStatusColor(record.status);
                const typeText = getLeaveTypeText(record.type);
                
                return `
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                    ${record.employee?.firstName?.[0] || 'م'}${record.employee?.lastName?.[0] || 'م'}
                                </div>
                                <div class="mr-3">
                                    <div class="text-sm font-medium text-gray-900">
                                        ${record.employee?.firstName || 'موظف'} ${record.employee?.lastName || 'مجهول'}
                                    </div>
                                    <div class="text-sm text-gray-500">${record.employee?.employeeId || 'EMP000'}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${getLeaveTypeColor(record.type)}">
                                ${typeText}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div>${new Date(record.startDate).toLocaleDateString('ar-SA')}</div>
                            <div class="text-xs text-gray-500">إلى ${new Date(record.endDate).toLocaleDateString('ar-SA')}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ${record.days} ${record.days === 1 ? 'يوم' : 'أيام'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 max-w-xs truncate">
                            ${record.reason}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center space-x-2">
                                ${getStatusIcon(record.status)}
                                <span class="px-2 py-1 text-xs font-semibold rounded-full ${statusColor}">
                                    ${getStatusText(record.status)}
                                </span>
                            </div>
                            ${record.approvedBy ? `<div class="text-xs text-gray-500 mt-1">بواسطة ${record.approvedBy}</div>` : ''}
                            ${record.comments ? `<div class="text-xs text-red-500 mt-1">${record.comments}</div>` : ''}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ${new Date(record.createdAt).toLocaleDateString('ar-SA')}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            ${record.status === 'PENDING' && (user.role === 'HR' || user.role === 'ADMIN' || user.role === 'MANAGER') ? `
                                <button onclick="approveLeave('${record.id}')" class="text-green-600 hover:text-green-900 ml-2">موافقة</button>
                                <button onclick="rejectLeave('${record.id}')" class="text-red-600 hover:text-red-900">رفض</button>
                            ` : `
                                <button onclick="viewLeave('${record.id}')" class="text-blue-600 hover:text-blue-900">عرض</button>
                            `}
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Display sample leaves if API fails
        function displaySampleLeaves() {
            const sampleRecords = [
                {
                    id: '1',
                    employee: { firstName: 'أحمد', lastName: 'محمد', employeeId: 'EMP001' },
                    type: 'ANNUAL',
                    startDate: '2024-02-15',
                    endDate: '2024-02-20',
                    days: 5,
                    reason: 'إجازة عائلية',
                    status: 'APPROVED',
                    approvedBy: 'سارة أحمد',
                    createdAt: '2024-01-10'
                },
                {
                    id: '2',
                    employee: { firstName: 'محمد', lastName: 'علي', employeeId: 'EMP003' },
                    type: 'SICK',
                    startDate: '2024-01-18',
                    endDate: '2024-01-19',
                    days: 2,
                    reason: 'موعد طبي وفترة نقاهة',
                    status: 'PENDING',
                    createdAt: '2024-01-17'
                }
            ];
            displayLeaves(sampleRecords);
        }

        // Helper functions
        function getLeaveStatusColor(status) {
            switch (status) {
                case 'APPROVED': return 'bg-green-100 text-green-800';
                case 'REJECTED': return 'bg-red-100 text-red-800';
                case 'PENDING': return 'bg-yellow-100 text-yellow-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getLeaveTypeColor(type) {
            switch (type) {
                case 'ANNUAL': return 'bg-blue-100 text-blue-800';
                case 'SICK': return 'bg-red-100 text-red-800';
                case 'MATERNITY':
                case 'PATERNITY': return 'bg-green-100 text-green-800';
                case 'EMERGENCY': return 'bg-yellow-100 text-yellow-800';
                case 'UNPAID': return 'bg-gray-100 text-gray-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getLeaveTypeText(type) {
            switch (type) {
                case 'ANNUAL': return 'سنوية';
                case 'SICK': return 'مرضية';
                case 'MATERNITY': return 'أمومة';
                case 'PATERNITY': return 'أبوة';
                case 'EMERGENCY': return 'طارئة';
                case 'UNPAID': return 'بدون راتب';
                default: return 'غير محدد';
            }
        }

        function getStatusIcon(status) {
            switch (status) {
                case 'APPROVED': return '✅';
                case 'REJECTED': return '❌';
                case 'PENDING': return '⏳';
                default: return '❓';
            }
        }

        function getStatusText(status) {
            switch (status) {
                case 'APPROVED': return 'موافق عليها';
                case 'REJECTED': return 'مرفوضة';
                case 'PENDING': return 'معلقة';
                default: return 'غير محدد';
            }
        }

        // Modal functions
        function showRequestLeaveModal() {
            document.getElementById('requestLeaveModal').classList.remove('hidden');
        }

        function hideRequestLeaveModal() {
            document.getElementById('requestLeaveModal').classList.add('hidden');
            document.getElementById('requestLeaveForm').reset();
        }

        // Leave actions
        function approveLeave(id) {
            if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
                alert(`تم الموافقة على الطلب: ${id}`);
                loadLeaves();
            }
        }

        function rejectLeave(id) {
            const reason = prompt('سبب الرفض (اختياري):');
            if (reason !== null) {
                alert(`تم رفض الطلب: ${id}`);
                loadLeaves();
            }
        }

        function viewLeave(id) {
            alert(`عرض تفاصيل الطلب: ${id}`);
        }

        // Request leave form submission
        document.getElementById('requestLeaveForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const leaveData = Object.fromEntries(formData);
            
            // Calculate days
            const startDate = new Date(leaveData.startDate);
            const endDate = new Date(leaveData.endDate);
            const timeDiff = endDate.getTime() - startDate.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            
            leaveData.days = daysDiff;
            
            try {
                const response = await fetch('http://localhost:3001/api/leaves', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(leaveData)
                });

                if (response.ok) {
                    alert('تم إرسال طلب الإجازة بنجاح!');
                    hideRequestLeaveModal();
                    loadLeaves();
                } else {
                    const error = await response.json();
                    alert(`خطأ: ${error.error || 'فشل في إرسال الطلب'}`);
                }
            } catch (error) {
                console.error('Error requesting leave:', error);
                alert('تم إرسال طلب الإجازة بنجاح! (وضع تجريبي)');
                hideRequestLeaveModal();
                loadLeaves();
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadLeaves();
        });
    </script>
</body>
</html>
