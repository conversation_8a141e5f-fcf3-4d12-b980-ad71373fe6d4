import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'

function App() {
  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            🏢 HR Management System
          </h1>
          <p className="text-gray-600 mb-6">
            نظام إدارة الموارد البشرية
          </p>
          <div className="space-y-4">
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              ✅ الخادم الخلفي يعمل على المنفذ 3001
            </div>
            <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
              ✅ الواجهة الأمامية تعمل على المنفذ 3002
            </div>
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
              🔧 النظام قيد التطوير
            </div>
          </div>
          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">حسابات تجريبية:</h3>
            <div className="text-sm text-left space-y-1">
              <p><strong>مدير:</strong> <EMAIL> / password123</p>
              <p><strong>موارد بشرية:</strong> <EMAIL> / password123</p>
              <p><strong>موظف:</strong> <EMAIL> / password123</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
