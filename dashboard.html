<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-800">🏢 نظام إدارة الموارد البشرية</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600"></span>
                    <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="flex">
        <aside class="w-64 bg-white shadow-lg h-screen">
            <nav class="mt-8">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-700 bg-blue-100 border-r-4 border-blue-500">
                    📊 لوحة التحكم
                </a>
                <a href="employees.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    👥 الموظفون
                </a>
                <a href="attendance.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    ⏰ الحضور
                </a>
                <a href="salary.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    💰 الرواتب
                </a>
                <a href="leaves.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📅 الإجازات
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📈 التقارير
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-800">لوحة التحكم</h2>
                <p class="text-gray-600">مرحباً بك في نظام إدارة الموارد البشرية</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                            <p class="text-2xl font-bold text-gray-900" id="totalEmployees">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">الحاضرون اليوم</p>
                            <p class="text-2xl font-bold text-gray-900" id="presentToday">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">في إجازة</p>
                            <p class="text-2xl font-bold text-gray-900" id="onLeave">-</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="mr-4">
                            <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                            <p class="text-2xl font-bold text-gray-900" id="pendingRequests">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">إحصائيات الحضور</h3>
                    <canvas id="attendanceChart" width="400" height="200"></canvas>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">توزيع الأقسام</h3>
                    <canvas id="departmentChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">الأنشطة الحديثة</h3>
                </div>
                <div class="p-6">
                    <div id="recentActivities" class="space-y-4">
                        <!-- Activities will be loaded here -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        if (!token) {
            window.location.href = 'index.html';
        }

        // Display user info
        document.getElementById('userInfo').textContent = 
            `مرحباً ${user.employee?.firstName || user.email} (${user.role})`;

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Load stats
                const statsResponse = await fetch('http://localhost:3001/api/employees/stats', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (statsResponse.ok) {
                    const stats = await statsResponse.json();
                    document.getElementById('totalEmployees').textContent = stats.totalEmployees || '6';
                    document.getElementById('presentToday').textContent = stats.presentToday || '5';
                    document.getElementById('onLeave').textContent = stats.onLeave || '1';
                    document.getElementById('pendingRequests').textContent = stats.pendingRequests || '2';
                } else {
                    // Fallback data
                    document.getElementById('totalEmployees').textContent = '6';
                    document.getElementById('presentToday').textContent = '5';
                    document.getElementById('onLeave').textContent = '1';
                    document.getElementById('pendingRequests').textContent = '2';
                }

                // Load recent activities
                loadRecentActivities();
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                // Show fallback data
                document.getElementById('totalEmployees').textContent = '6';
                document.getElementById('presentToday').textContent = '5';
                document.getElementById('onLeave').textContent = '1';
                document.getElementById('pendingRequests').textContent = '2';
            }
        }

        // Load recent activities
        function loadRecentActivities() {
            const activities = [
                { type: 'login', message: 'تم تسجيل دخول أحمد محمد', time: 'منذ 5 دقائق' },
                { type: 'leave', message: 'طلب إجازة من سارة أحمد', time: 'منذ 30 دقيقة' },
                { type: 'attendance', message: 'تسجيل حضور متأخر لمحمد علي', time: 'منذ ساعة' },
                { type: 'salary', message: 'تم معالجة راتب ديسمبر', time: 'منذ ساعتين' }
            ];

            const container = document.getElementById('recentActivities');
            container.innerHTML = activities.map(activity => `
                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <div class="w-2 h-2 bg-blue-500 rounded-full ml-3"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">${activity.message}</p>
                        <p class="text-xs text-gray-500">${activity.time}</p>
                    </div>
                </div>
            `).join('');
        }

        // Initialize charts
        function initCharts() {
            // Attendance Chart
            const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
            new Chart(attendanceCtx, {
                type: 'line',
                data: {
                    labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                    datasets: [{
                        label: 'الحضور',
                        data: [5, 6, 5, 6, 5, 4, 3],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // Department Chart
            const departmentCtx = document.getElementById('departmentChart').getContext('2d');
            new Chart(departmentCtx, {
                type: 'doughnut',
                data: {
                    labels: ['الهندسة', 'الموارد البشرية', 'التسويق', 'المبيعات'],
                    datasets: [{
                        data: [3, 1, 1, 1],
                        backgroundColor: [
                            'rgb(59, 130, 246)',
                            'rgb(16, 185, 129)',
                            'rgb(245, 158, 11)',
                            'rgb(239, 68, 68)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            initCharts();
        });
    </script>
</body>
</html>
