{"name": "hr-frontend", "version": "1.0.0", "description": "HR Management System Frontend", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.16", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "jspdf": "^2.5.1", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-calendar": "^4.7.0", "react-datepicker": "^4.25.0", "react-dom": "^18.3.1", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-query": "^3.39.3", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "recharts": "^2.8.0", "tailwind-merge": "^2.1.0", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@types/react": "^18.3.23", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.3.7", "@types/react-table": "^7.7.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}}