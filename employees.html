<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - نظام إدارة الموارد البشرية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold text-gray-800">🏢 نظام إدارة الموارد البشرية</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-600"></span>
                    <button onclick="logout()" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                        تسجيل الخروج
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Sidebar -->
    <div class="flex">
        <aside class="w-64 bg-white shadow-lg h-screen">
            <nav class="mt-8">
                <a href="dashboard.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📊 لوحة التحكم
                </a>
                <a href="employees.html" class="flex items-center px-6 py-3 text-gray-700 bg-blue-100 border-r-4 border-blue-500">
                    👥 الموظفون
                </a>
                <a href="attendance.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    ⏰ الحضور
                </a>
                <a href="salary.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    💰 الرواتب
                </a>
                <a href="leaves.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📅 الإجازات
                </a>
                <a href="reports.html" class="flex items-center px-6 py-3 text-gray-700 hover:bg-gray-100">
                    📈 التقارير
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-8">
            <div class="mb-8">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-800">إدارة الموظفين</h2>
                        <p class="text-gray-600">إدارة بيانات الموظفين والمعلومات الشخصية</p>
                    </div>
                    <button onclick="showAddEmployeeModal()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                        ➕ إضافة موظف جديد
                    </button>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <input type="text" id="searchInput" placeholder="البحث عن موظف..." 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <select id="departmentFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الأقسام</option>
                            <option value="Engineering">الهندسة</option>
                            <option value="Human Resources">الموارد البشرية</option>
                            <option value="Marketing">التسويق</option>
                            <option value="Sales">المبيعات</option>
                        </select>
                    </div>
                    <div>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="true">نشط</option>
                            <option value="false">غير نشط</option>
                        </select>
                    </div>
                    <div>
                        <button onclick="loadEmployees()" class="w-full bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600">
                            🔍 بحث
                        </button>
                    </div>
                </div>
            </div>

            <!-- Employees Table -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">قائمة الموظفين</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الموظف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنصب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القسم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التوظيف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الراتب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- Employees will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div id="loadingMessage" class="text-center py-8 text-gray-500">
                    جاري تحميل البيانات...
                </div>
            </div>
        </main>
    </div>

    <!-- Add Employee Modal -->
    <div id="addEmployeeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">إضافة موظف جديد</h3>
                </div>
                <form id="addEmployeeForm" class="p-6 space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                            <input type="text" name="firstName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم العائلة</label>
                            <input type="text" name="lastName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="tel" name="phone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">المنصب</label>
                        <input type="text" name="position" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">القسم</label>
                        <select name="department" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر القسم</option>
                            <option value="Engineering">الهندسة</option>
                            <option value="Human Resources">الموارد البشرية</option>
                            <option value="Marketing">التسويق</option>
                            <option value="Sales">المبيعات</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">الراتب</label>
                        <input type="number" name="salary" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex justify-end space-x-4 pt-4">
                        <button type="button" onclick="hideAddEmployeeModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                            إلغاء
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                            إضافة الموظف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Check authentication
        const token = localStorage.getItem('authToken');
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        
        if (!token) {
            window.location.href = 'index.html';
        }

        // Display user info
        document.getElementById('userInfo').textContent = 
            `مرحباً ${user.employee?.firstName || user.email} (${user.role})`;

        // Logout function
        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            window.location.href = 'index.html';
        }

        // Load employees
        async function loadEmployees() {
            const loadingMessage = document.getElementById('loadingMessage');
            const tableBody = document.getElementById('employeesTableBody');
            
            loadingMessage.style.display = 'block';
            tableBody.innerHTML = '';

            try {
                const response = await fetch('http://localhost:3001/api/employees', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayEmployees(data.employees || []);
                } else {
                    // Show sample data if API fails
                    displaySampleEmployees();
                }
            } catch (error) {
                console.error('Error loading employees:', error);
                displaySampleEmployees();
            }

            loadingMessage.style.display = 'none';
        }

        // Display employees in table
        function displayEmployees(employees) {
            const tableBody = document.getElementById('employeesTableBody');
            
            if (employees.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center py-8 text-gray-500">لا توجد موظفين</td></tr>';
                return;
            }

            tableBody.innerHTML = employees.map(employee => `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                                ${employee.firstName[0]}${employee.lastName[0]}
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900">${employee.firstName} ${employee.lastName}</div>
                                <div class="text-sm text-gray-500">${employee.email}</div>
                                <div class="text-xs text-gray-400">${employee.employeeId}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.position}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.department}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${new Date(employee.hireDate).toLocaleDateString('ar-SA')}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.salary?.toLocaleString()} ريال</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold rounded-full ${employee.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${employee.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="viewEmployee('${employee.id}')" class="text-blue-600 hover:text-blue-900 ml-2">عرض</button>
                        <button onclick="editEmployee('${employee.id}')" class="text-green-600 hover:text-green-900 ml-2">تعديل</button>
                        <button onclick="deleteEmployee('${employee.id}')" class="text-red-600 hover:text-red-900">حذف</button>
                    </td>
                </tr>
            `).join('');
        }

        // Display sample employees if API fails
        function displaySampleEmployees() {
            const sampleEmployees = [
                {
                    id: '1', employeeId: 'EMP001', firstName: 'أحمد', lastName: 'محمد',
                    email: '<EMAIL>', position: 'مدير النظام', department: 'تقنية المعلومات',
                    hireDate: '2020-01-01', salary: 100000, isActive: true
                },
                {
                    id: '2', employeeId: 'EMP002', firstName: 'سارة', lastName: 'أحمد',
                    email: '<EMAIL>', position: 'مدير الموارد البشرية', department: 'الموارد البشرية',
                    hireDate: '2021-02-15', salary: 85000, isActive: true
                },
                {
                    id: '3', employeeId: 'EMP003', firstName: 'محمد', lastName: 'علي',
                    email: '<EMAIL>', position: 'مدير الهندسة', department: 'الهندسة',
                    hireDate: '2019-06-01', salary: 95000, isActive: true
                }
            ];
            displayEmployees(sampleEmployees);
        }

        // Modal functions
        function showAddEmployeeModal() {
            document.getElementById('addEmployeeModal').classList.remove('hidden');
        }

        function hideAddEmployeeModal() {
            document.getElementById('addEmployeeModal').classList.add('hidden');
            document.getElementById('addEmployeeForm').reset();
        }

        // Employee actions
        function viewEmployee(id) {
            alert(`عرض تفاصيل الموظف: ${id}`);
        }

        function editEmployee(id) {
            alert(`تعديل الموظف: ${id}`);
        }

        function deleteEmployee(id) {
            if (confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                alert(`حذف الموظف: ${id}`);
                loadEmployees();
            }
        }

        // Add employee form submission
        document.getElementById('addEmployeeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const employeeData = Object.fromEntries(formData);
            
            try {
                const response = await fetch('http://localhost:3001/api/employees', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(employeeData)
                });

                if (response.ok) {
                    alert('تم إضافة الموظف بنجاح!');
                    hideAddEmployeeModal();
                    loadEmployees();
                } else {
                    const error = await response.json();
                    alert(`خطأ: ${error.error || 'فشل في إضافة الموظف'}`);
                }
            } catch (error) {
                console.error('Error adding employee:', error);
                alert('خطأ في الاتصال');
            }
        });

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadEmployees();
        });
    </script>
</body>
</html>
